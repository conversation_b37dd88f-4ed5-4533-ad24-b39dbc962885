<%- include('../partials/header') %>

<div class="columns">
    <div class="column">
        <h2 class="title is-2">个人值班记录</h2>
        <p class="has-text-grey">当前查看：<%= currentYear %>年<%= currentMonth %>月</p>
    </div>
</div>

<div class="columns is-multiline mb-4">
    <div class="column is-3">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">本年值班</h5>
                <h3 class="title is-3 has-text-primary"><%= stats.yearDutyCount %></h3>
                <p class="subtitle is-6">天</p>
            </div>
        </div>
    </div>
    <div class="column is-3">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">本年调休</h5>
                <h3 class="title is-3 has-text-success"><%= stats.yearLeaveCount %></h3>
                <p class="subtitle is-6">天</p>
            </div>
        </div>
    </div>
    <div class="column is-3">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">本月值班</h5>
                <h3 class="title is-3 has-text-info"><%= stats.monthDutyCount %></h3>
                <p class="subtitle is-6">天</p>
            </div>
        </div>
    </div>
    <div class="column is-3">
        <div class="card has-text-centered">
            <div class="card-content">
                <h5 class="title is-5">剩余调休</h5>
                <h3 class="title is-3 has-text-warning"><%= stats.remainingLeaveCount %></h3>
                <p class="subtitle is-6">天</p>
            </div>
        </div>
    </div>
</div>

<div class="columns">
    <div class="column">
        <div class="card">
            <div class="card-header">
                <div class="level">
                    <div class="level-left">
                        <h5 class="title is-5">值班记录</h5>
                    </div>
                    <div class="level-right">
                        <div class="field is-grouped">
                            <div class="control">
                                <div class="select is-small">
                                    <select id="yearSelect">
                                        <%
                                        const startYear = 2024;
                                        const endYear = new Date().getFullYear() + 1;
                                        for(let year = endYear; year >= startYear; year--) { %>
                                        <option value="<%= year %>" <%= year === currentYear ? 'selected' : '' %>><%= year %>年</option>
                                        <% } %>
                                    </select>
                                </div>
                            </div>
                            <div class="control">
                                <div class="select is-small">
                                    <select id="monthSelect">
                                        <% for(let i = 1; i <= 12; i++) { %>
                                        <option value="<%= i %>" <%= i === currentMonth ? 'selected' : '' %>><%= i %>月</option>
                                        <% } %>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-content">
                <% if (records.length > 0) { %>
                <div class="table-container">
                    <table class="table is-striped is-fullwidth">
                        <thead>
                            <tr>
                                <th>值班日期</th>
                                <th>调休日期</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% records.forEach(record => { %>
                            <tr>
                                <td><%= (() => {
                                    const date = new Date(record.duty_date);
                                    const year = date.getFullYear();
                                    const month = String(date.getMonth() + 1).padStart(2, '0');
                                    const day = String(date.getDate()).padStart(2, '0');
                                    return `${year}-${month}-${day}`;
                                })() %></td>
                                <td><%= record.leave_date ? (() => {
                                    const date = new Date(record.leave_date);
                                    const year = date.getFullYear();
                                    const month = String(date.getMonth() + 1).padStart(2, '0');
                                    const day = String(date.getDate()).padStart(2, '0');
                                    return `${year}-${month}-${day}`;
                                })() : '未设置' %></td>
                            </tr>
                            <% }) %>
                        </tbody>
                    </table>
                </div>
                <% } else { %>
                <p class="has-text-centered has-text-grey">本月暂无值班记录</p>
                <% } %>
            </div>
        </div>
    </div>
</div>

<%- include('../partials/footer') %>
